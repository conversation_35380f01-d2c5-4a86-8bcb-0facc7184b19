{"name": "@zenera/webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@zenera/sharing": "workspace:*", "@zenera/ui-components": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-resources-to-backend": "^1.2.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-i18next": "^13.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.47", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.0", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5"}}