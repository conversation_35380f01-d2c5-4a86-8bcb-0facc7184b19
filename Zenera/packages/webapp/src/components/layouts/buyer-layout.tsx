"use client";

import React from 'react';
import { BuyerHeader } from '@/components/buyer/layout/header/buyer-header';
import { BuyerFooter } from '@/components/buyer/layout/footer/buyer-footer';

interface BuyerLayoutProps {
  children: React.ReactNode;
}

/**
 * Buyer Layout Component
 * Layout chính cho buyer với header, navigation và footer
 * Đã được chia nhỏ theo pattern của Medoo
 */
export const BuyerLayout: React.FC<BuyerLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <BuyerHeader />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <BuyerFooter />
    </div>
  );
};
