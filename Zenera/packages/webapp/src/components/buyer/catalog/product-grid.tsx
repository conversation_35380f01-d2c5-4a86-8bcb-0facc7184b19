"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useZeneraTranslation } from '@/lib/i18n/hooks/useZeneraTranslation';
import { useCartStore } from '@/lib/stores/cart-store';
import { ProductCard } from '@zenera/ui-components/compositions';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image?: string;
  rating: number;
  reviewCount: number;
  seller?: { name: string };
  isAvailable: boolean;
}

interface ProductGridProps {
  products: Product[];
  isLoading: boolean;
  viewMode: 'grid' | 'list';
  onClearFilters: () => void;
}

/**
 * Product Grid Component
 * Hiển thị danh sách sản phẩm - chia nhỏ từ ProductCatalog
 */
export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  isLoading,
  viewMode,
  onClearFilters,
}) => {
  const router = useRouter();
  const { t } = useZeneraTranslation('ecommerce');
  const { addItem: addToCart } = useCartStore();

  const handleAddToCart = async (product: Product) => {
    try {
      await addToCart(product, 1);
      // Show success message
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  };

  if (isLoading) {
    return (
      <div className={`grid gap-6 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
          : 'grid-cols-1'
      }`}>
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">{t('noProductsFound')}</p>
        <Button variant="outline" onClick={onClearFilters} className="mt-4">
          {t('clearFilters')}
        </Button>
      </div>
    );
  }

  return (
    <div className={`grid gap-6 ${
      viewMode === 'grid' 
        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
        : 'grid-cols-1'
    }`}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          variant={viewMode === 'list' ? 'horizontal' : 'default'}
          onAddToCart={() => handleAddToCart(product)}
          onAddToWishlist={(id) => console.log('Add to wishlist:', id)}
          onQuickView={(id) => router.push(`/products/${id}`)}
        />
      ))}
    </div>
  );
};
