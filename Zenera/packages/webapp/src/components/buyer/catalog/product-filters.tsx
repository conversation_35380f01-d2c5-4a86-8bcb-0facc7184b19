"use client";

import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Star } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/simple-translation';

interface FilterState {
  categories: string[];
  priceRange: [number, number];
  brands: string[];
  rating: number;
  inStock: boolean;
}

interface ProductFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  categories: Array<{ id: string; name: string; count: number }>;
  brands: Array<{ id: string; name: string; count: number }>;
}

/**
 * Product Filters Component
 * Bộ lọc sản phẩm - chia nhỏ từ ProductCatalog
 */
export const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFiltersChange,
  categories,
  brands,
}) => {
  const { t } = useZeneraTranslation('ecommerce');

  const handleCategoryChange = (category: string, checked: boolean) => {
    onFiltersChange({
      ...filters,
      categories: checked 
        ? [...filters.categories, category]
        : filters.categories.filter(c => c !== category)
    });
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    onFiltersChange({
      ...filters,
      brands: checked 
        ? [...filters.brands, brand]
        : filters.brands.filter(b => b !== brand)
    });
  };

  const handlePriceRangeChange = (value: [number, number]) => {
    onFiltersChange({ ...filters, priceRange: value });
  };

  const clearFilters = () => {
    onFiltersChange({
      categories: [],
      priceRange: [0, 1000],
      brands: [],
      rating: 0,
      inStock: false,
    });
  };

  return (
    <div className="space-y-6">
      {/* Categories */}
      <div>
        <h3 className="font-medium mb-3">{t('categories')}</h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={filters.categories.includes(category.id)}
                onCheckedChange={(checked) => 
                  handleCategoryChange(category.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`category-${category.id}`}
                className="text-sm font-normal"
              >
                {category.name} ({category.count})
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Price Range */}
      <div>
        <h3 className="font-medium mb-3">{t('priceRange')}</h3>
        <div className="space-y-4">
          <Slider
            value={filters.priceRange}
            onValueChange={handlePriceRangeChange}
            max={1000}
            step={10}
            className="w-full"
          />
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>${filters.priceRange[0]}</span>
            <span>${filters.priceRange[1]}</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Brands */}
      <div>
        <h3 className="font-medium mb-3">{t('brands')}</h3>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center space-x-2">
              <Checkbox
                id={`brand-${brand.id}`}
                checked={filters.brands.includes(brand.id)}
                onCheckedChange={(checked) => 
                  handleBrandChange(brand.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`brand-${brand.id}`}
                className="text-sm font-normal"
              >
                {brand.name} ({brand.count})
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Rating */}
      <div>
        <h3 className="font-medium mb-3">{t('rating')}</h3>
        <div className="space-y-2">
          {[4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={filters.rating === rating}
                onCheckedChange={(checked) => 
                  onFiltersChange({ ...filters, rating: checked ? rating : 0 })
                }
              />
              <Label htmlFor={`rating-${rating}`} className="flex items-center space-x-1">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm">& {t('up')}</span>
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Availability */}
      <div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="in-stock"
            checked={filters.inStock}
            onCheckedChange={(checked) => 
              onFiltersChange({ ...filters, inStock: checked as boolean })
            }
          />
          <Label htmlFor="in-stock">{t('inStockOnly')}</Label>
        </div>
      </div>

      {/* Clear Filters */}
      <Button variant="outline" onClick={clearFilters} className="w-full">
        {t('clearFilters')}
      </Button>
    </div>
  );
};
