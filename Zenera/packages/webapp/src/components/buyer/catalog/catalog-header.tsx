"use client";

import React from 'react';
import { Filter, Grid, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useZeneraTranslation } from '@/lib/i18n/hooks/useZeneraTranslation';

interface CatalogHeaderProps {
  title: string;
  productCount: number;
  sortBy: string;
  onSortChange: (value: string) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  isFilterOpen: boolean;
  onFilterOpenChange: (open: boolean) => void;
  filterPanel: React.ReactNode;
}

/**
 * Catalog Header Component
 * Header cho product catalog với controls - chia nhỏ từ ProductCatalog
 */
export const CatalogHeader: React.FC<CatalogHeaderProps> = ({
  title,
  productCount,
  sortBy,
  onSortChange,
  viewMode,
  onViewModeChange,
  isFilterOpen,
  onFilterOpenChange,
  filterPanel,
}) => {
  const { t } = useZeneraTranslation('ecommerce');

  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">{title}</h1>
        <p className="text-muted-foreground">
          {t('productsFound', { count: productCount })}
        </p>
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-4 mt-4 md:mt-0">
        {/* Sort */}
        <Select value={sortBy} onValueChange={onSortChange}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder={t('sortBy')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="featured">{t('featured')}</SelectItem>
            <SelectItem value="price-low">{t('priceLowToHigh')}</SelectItem>
            <SelectItem value="price-high">{t('priceHighToLow')}</SelectItem>
            <SelectItem value="newest">{t('newest')}</SelectItem>
            <SelectItem value="rating">{t('topRated')}</SelectItem>
          </SelectContent>
        </Select>

        {/* View Mode */}
        <div className="flex border rounded-md">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        {/* Mobile Filter */}
        <Sheet open={isFilterOpen} onOpenChange={onFilterOpenChange}>
          <SheetTrigger asChild>
            <Button variant="outline" size="sm" className="md:hidden">
              <Filter className="h-4 w-4 mr-2" />
              {t('filters')}
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80">
            <SheetHeader>
              <SheetTitle>{t('filters')}</SheetTitle>
              <SheetDescription>
                {t('filterDescription')}
              </SheetDescription>
            </SheetHeader>
            <div className="mt-6">
              {filterPanel}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
};
