"use client";

import React from 'react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Camera,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  Shield,
  Bell,
  CreditCard,
  Package,
  Heart
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { useHTranslation } from '@/lib/i18n/hooks/useHTranslation';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Profile form schema
const profileSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  date_of_birth: z.string().optional(),
  bio: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

// Address form schema
const addressSchema = z.object({
  type: z.enum(['home', 'work', 'other']),
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(1, 'Phone is required'),
  address_line_1: z.string().min(1, 'Address is required'),
  address_line_2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  postal_code: z.string().min(1, 'Postal code is required'),
  country: z.string().min(1, 'Country is required'),
  is_default: z.boolean().default(false),
});

type AddressFormData = z.infer<typeof addressSchema>;

/**
 * User Profile Component
 * Quản lý hồ sơ người dùng với tabs cho thông tin cá nhân, địa chỉ, cài đặt
 * Tham khảo từ Medoo user profile patterns
 */
export const UserProfile: React.FC = () => {
  const router = useRouter();
  const { t } = useHTranslation('ecommerce');
  const { toast } = useToast();
  const { user, updateProfile } = useAuthStore();

  // State
  const [isEditing, setIsEditing] = useState(false);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('profile');

  // Profile form
  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      date_of_birth: user?.date_of_birth || '',
      bio: user?.bio || '',
    },
  });

  // Address form
  const addressForm = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      type: 'home',
      name: '',
      phone: '',
      address_line_1: '',
      address_line_2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'Vietnam',
      is_default: false,
    },
  });

  const handleProfileSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfile(data);
      setIsEditing(false);
      toast({
        title: t('success'),
        description: t('profileUpdated'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToUpdateProfile'),
        variant: "destructive",
      });
    }
  };

  const handleAddressSubmit = async (data: AddressFormData) => {
    try {
      // TODO: Implement address API calls
      console.log('Address data:', data);
      setIsAddingAddress(false);
      setEditingAddressId(null);
      addressForm.reset();
      toast({
        title: t('success'),
        description: t('addressSaved'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToSaveAddress'),
        variant: "destructive",
      });
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    try {
      // TODO: Implement delete address API
      console.log('Delete address:', addressId);
      toast({
        title: t('success'),
        description: t('addressDeleted'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToDeleteAddress'),
        variant: "destructive",
      });
    }
  };

  const mockAddresses = [
    {
      id: '1',
      type: 'home',
      name: 'John Doe',
      phone: '+84 123 456 789',
      address_line_1: '123 Nguyen Trai Street',
      address_line_2: 'Apartment 4B',
      city: 'Ho Chi Minh City',
      state: 'Ho Chi Minh',
      postal_code: '700000',
      country: 'Vietnam',
      is_default: true,
    },
    {
      id: '2',
      type: 'work',
      name: 'John Doe',
      phone: '+84 987 654 321',
      address_line_1: '456 Le Loi Boulevard',
      city: 'Ho Chi Minh City',
      state: 'Ho Chi Minh',
      postal_code: '700000',
      country: 'Vietnam',
      is_default: false,
    },
  ];

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">{t('pleaseLogin')}</h1>
        <Button onClick={() => router.push('/auth/login')}>
          {t('login')}
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-6 mb-8">
          <div className="relative">
            <Avatar className="h-24 w-24">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-2xl">
                {user.first_name?.[0]}{user.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <Button
              size="sm"
              variant="outline"
              className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
            >
              <Camera className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">
              {user.first_name} {user.last_name}
            </h1>
            <p className="text-muted-foreground">{user.email}</p>
            <div className="flex items-center space-x-4 mt-2">
              <Badge variant="secondary">
                {t('memberSince')} {new Date(user.created_at || '').getFullYear()}
              </Badge>
              {user.customer_info?.verified && (
                <Badge variant="default">
                  <Shield className="h-3 w-3 mr-1" />
                  {t('verified')}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="profile">{t('profile')}</TabsTrigger>
            <TabsTrigger value="addresses">{t('addresses')}</TabsTrigger>
            <TabsTrigger value="orders">{t('orders')}</TabsTrigger>
            <TabsTrigger value="wishlist">{t('wishlist')}</TabsTrigger>
            <TabsTrigger value="settings">{t('settings')}</TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="mt-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>{t('personalInformation')}</CardTitle>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? (
                    <>
                      <X className="h-4 w-4 mr-2" />
                      {t('cancel')}
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-2" />
                      {t('edit')}
                    </>
                  )}
                </Button>
              </CardHeader>
              <CardContent>
                <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="first_name">{t('firstName')}</Label>
                      <Input
                        id="first_name"
                        {...profileForm.register('first_name')}
                        disabled={!isEditing}
                      />
                      {profileForm.formState.errors.first_name && (
                        <p className="text-sm text-red-500 mt-1">
                          {profileForm.formState.errors.first_name.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="last_name">{t('lastName')}</Label>
                      <Input
                        id="last_name"
                        {...profileForm.register('last_name')}
                        disabled={!isEditing}
                      />
                      {profileForm.formState.errors.last_name && (
                        <p className="text-sm text-red-500 mt-1">
                          {profileForm.formState.errors.last_name.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="email">{t('email')}</Label>
                      <Input
                        id="email"
                        type="email"
                        {...profileForm.register('email')}
                        disabled={!isEditing}
                      />
                      {profileForm.formState.errors.email && (
                        <p className="text-sm text-red-500 mt-1">
                          {profileForm.formState.errors.email.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="phone">{t('phone')}</Label>
                      <Input
                        id="phone"
                        {...profileForm.register('phone')}
                        disabled={!isEditing}
                      />
                    </div>

                    <div>
                      <Label htmlFor="date_of_birth">{t('dateOfBirth')}</Label>
                      <Input
                        id="date_of_birth"
                        type="date"
                        {...profileForm.register('date_of_birth')}
                        disabled={!isEditing}
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="bio">{t('bio')}</Label>
                      <Textarea
                        id="bio"
                        {...profileForm.register('bio')}
                        disabled={!isEditing}
                        rows={3}
                      />
                    </div>
                  </div>

                  {isEditing && (
                    <div className="flex justify-end mt-6">
                      <Button type="submit">
                        <Save className="h-4 w-4 mr-2" />
                        {t('saveChanges')}
                      </Button>
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Addresses Tab */}
          <TabsContent value="addresses" className="mt-6">
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">{t('shippingAddresses')}</h2>
                <Button onClick={() => setIsAddingAddress(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('addAddress')}
                </Button>
              </div>

              {/* Add/Edit Address Form */}
              {(isAddingAddress || editingAddressId) && (
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {isAddingAddress ? t('addNewAddress') : t('editAddress')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={addressForm.handleSubmit(handleAddressSubmit)}>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="name">{t('fullName')}</Label>
                          <Input
                            id="name"
                            {...addressForm.register('name')}
                          />
                        </div>

                        <div>
                          <Label htmlFor="phone">{t('phone')}</Label>
                          <Input
                            id="phone"
                            {...addressForm.register('phone')}
                          />
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor="address_line_1">{t('addressLine1')}</Label>
                          <Input
                            id="address_line_1"
                            {...addressForm.register('address_line_1')}
                          />
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor="address_line_2">{t('addressLine2')}</Label>
                          <Input
                            id="address_line_2"
                            {...addressForm.register('address_line_2')}
                          />
                        </div>

                        <div>
                          <Label htmlFor="city">{t('city')}</Label>
                          <Input
                            id="city"
                            {...addressForm.register('city')}
                          />
                        </div>

                        <div>
                          <Label htmlFor="state">{t('state')}</Label>
                          <Input
                            id="state"
                            {...addressForm.register('state')}
                          />
                        </div>

                        <div>
                          <Label htmlFor="postal_code">{t('postalCode')}</Label>
                          <Input
                            id="postal_code"
                            {...addressForm.register('postal_code')}
                          />
                        </div>

                        <div>
                          <Label htmlFor="country">{t('country')}</Label>
                          <Input
                            id="country"
                            {...addressForm.register('country')}
                          />
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mt-4">
                        <input
                          type="checkbox"
                          id="is_default"
                          {...addressForm.register('is_default')}
                        />
                        <Label htmlFor="is_default">{t('setAsDefault')}</Label>
                      </div>

                      <div className="flex justify-end space-x-2 mt-6">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setIsAddingAddress(false);
                            setEditingAddressId(null);
                            addressForm.reset();
                          }}
                        >
                          {t('cancel')}
                        </Button>
                        <Button type="submit">
                          {t('saveAddress')}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              )}

              {/* Addresses List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockAddresses.map((address) => (
                  <Card key={address.id}>
                    <CardContent className="pt-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center space-x-2">
                          <Badge variant={address.is_default ? "default" : "secondary"}>
                            {t(address.type)}
                          </Badge>
                          {address.is_default && (
                            <Badge variant="outline">{t('default')}</Badge>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingAddressId(address.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAddress(address.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2 text-sm">
                        <p className="font-medium">{address.name}</p>
                        <p>{address.phone}</p>
                        <p>{address.address_line_1}</p>
                        {address.address_line_2 && <p>{address.address_line_2}</p>}
                        <p>
                          {address.city}, {address.state} {address.postal_code}
                        </p>
                        <p>{address.country}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  {t('orderHistory')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">{t('noOrdersYet')}</p>
                  <Button 
                    className="mt-4"
                    onClick={() => router.push('/products')}
                  >
                    {t('startShopping')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Wishlist Tab */}
          <TabsContent value="wishlist" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  {t('wishlist')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">{t('noWishlistItems')}</p>
                  <Button 
                    className="mt-4"
                    onClick={() => router.push('/products')}
                  >
                    {t('browseProducts')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="mt-6">
            <div className="space-y-6">
              {/* Notifications */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Bell className="h-5 w-5 mr-2" />
                    {t('notifications')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('emailNotifications')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('emailNotificationsDescription')}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('orderUpdates')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('orderUpdatesDescription')}
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('promotionalEmails')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('promotionalEmailsDescription')}
                      </p>
                    </div>
                    <Switch />
                  </div>
                </CardContent>
              </Card>

              {/* Security */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2" />
                    {t('security')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    {t('changePassword')}
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    {t('twoFactorAuth')}
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    {t('loginHistory')}
                  </Button>
                </CardContent>
              </Card>

              {/* Payment Methods */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2" />
                    {t('paymentMethods')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <CreditCard className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">{t('noPaymentMethods')}</p>
                    <Button className="mt-4">
                      {t('addPaymentMethod')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
