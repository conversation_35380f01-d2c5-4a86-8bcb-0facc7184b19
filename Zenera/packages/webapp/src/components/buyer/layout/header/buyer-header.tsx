"use client";

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useCartStore } from '@/lib/stores/cart-store';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { BuyerLogo } from './buyer-logo';
import { BuyerNavigation } from './buyer-navigation';
import { BuyerSearchBar } from './buyer-search-bar';
import { BuyerCartIcon } from './buyer-cart-icon';
import { BuyerUserMenu } from './buyer-user-menu';
import { BuyerMobileMenu } from './buyer-mobile-menu';

/**
 * Buyer Header Component
 * Header chính cho buyer layout
 * Chia nhỏ theo pattern của Medoo
 */
export const BuyerHeader: React.FC = () => {
  const router = useRouter();
  const { t } = useZeneraTranslation('ecommerce');
  const { user, isAuthenticated } = useAuthStore();
  const { summary } = useCartStore();

  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-4">
            <BuyerLogo />
          </div>

          {/* Desktop Navigation */}
          <BuyerNavigation className="hidden md:flex" />

          {/* Search Bar */}
          <BuyerSearchBar className="hidden md:flex flex-1 max-w-md mx-8" />

          {/* Right Actions */}
          <div className="flex items-center space-x-4">
            {/* Wishlist */}
            <Button variant="ghost" size="sm" asChild className="hidden sm:flex">
              <Link href="/wishlist">
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </Link>
            </Button>

            {/* Cart */}
            <BuyerCartIcon totalItems={summary.totalItems} />

            {/* User Menu */}
            <BuyerUserMenu 
              user={user} 
              isAuthenticated={isAuthenticated} 
            />

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <BuyerMobileMenu onClose={() => setIsMobileMenuOpen(false)} />
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};
