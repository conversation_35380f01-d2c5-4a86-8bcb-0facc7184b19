"use client";

import React from 'react';
import Link from 'next/link';
import { Search, Home, Package, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useZeneraTranslation } from '@/lib/i18n/hooks/useZeneraTranslation';
import { useRouter } from 'next/navigation';

interface BuyerMobileMenuProps {
  onClose: () => void;
}

/**
 * Buyer Mobile Menu Component
 * Mobile menu cho buyer header
 */
export const BuyerMobileMenu: React.FC<BuyerMobileMenuProps> = ({ onClose }) => {
  const router = useRouter();
  const { t } = useZeneraTranslation('ecommerce');
  const [searchQuery, setSearchQuery] = React.useState('');

  const navigationItems = [
    { href: '/', label: t('home'), icon: Home },
    { href: '/products', label: t('products'), icon: Package },
    { href: '/categories', label: t('categories'), icon: Package },
    { href: '/deals', label: t('deals'), icon: Star },
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      onClose();
    }
  };

  return (
    <div className="flex flex-col space-y-4 mt-8">
      {/* Mobile Search */}
      <form onSubmit={handleSearch} className="flex">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={t('searchProducts')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </form>

      {/* Mobile Navigation */}
      <nav className="flex flex-col space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className="flex items-center space-x-3 px-3 py-2 rounded-md hover:bg-accent"
              onClick={onClose}
            >
              <Icon className="h-5 w-5" />
              <span>{item.label}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
};
