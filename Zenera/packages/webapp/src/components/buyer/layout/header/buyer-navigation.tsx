"use client";

import React from 'react';
import Link from 'next/link';
import { Home, Package, Star } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface BuyerNavigationProps {
  className?: string;
}

/**
 * Buyer Navigation Component
 * Navigation menu cho buyer header
 */
export const BuyerNavigation: React.FC<BuyerNavigationProps> = ({ className }) => {
  const { t } = useZeneraTranslation('ecommerce');

  const navigationItems = [
    { href: '/', label: t('home'), icon: Home },
    { href: '/products', label: t('products'), icon: Package },
    { href: '/categories', label: t('categories'), icon: Package },
    { href: '/deals', label: t('deals'), icon: Star },
  ];

  return (
    <nav className={cn("items-center space-x-6", className)}>
      {navigationItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
        >
          {item.label}
        </Link>
      ))}
    </nav>
  );
};
