"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useZeneraTranslation } from '@/lib/i18n/hooks/useZeneraTranslation';
import { cn } from '@/lib/utils';

interface BuyerSearchBarProps {
  className?: string;
}

/**
 * Buyer Search Bar Component
 * Search bar cho buyer header
 */
export const BuyerSearchBar: React.FC<BuyerSearchBarProps> = ({ className }) => {
  const router = useRouter();
  const { t } = useZeneraTranslation('ecommerce');
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <div className={cn(className)}>
      <form onSubmit={handleSearch} className="flex w-full">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={t('searchProducts')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button type="submit" variant="ghost" size="sm" className="ml-2">
          <Search className="h-4 w-4" />
        </Button>
      </form>
    </div>
  );
};
