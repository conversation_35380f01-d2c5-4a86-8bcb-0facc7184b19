"use client";

import React from 'react';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface BuyerCartIconProps {
  totalItems: number;
}

/**
 * Buyer Cart Icon Component
 * Cart icon với badge hiển thị số lượng items
 */
export const BuyerCartIcon: React.FC<BuyerCartIconProps> = ({ totalItems }) => {
  return (
    <Button variant="ghost" size="sm" asChild className="relative">
      <Link href="/cart">
        <ShoppingCart className="h-5 w-5" />
        {totalItems > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {totalItems > 99 ? '99+' : totalItems}
          </Badge>
        )}
      </Link>
    </Button>
  );
};
