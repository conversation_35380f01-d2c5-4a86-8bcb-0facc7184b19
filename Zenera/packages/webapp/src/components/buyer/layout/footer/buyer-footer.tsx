"use client";

import React from 'react';
import Link from 'next/link';
import { useZeneraTranslation } from '@/lib/i18n/hooks/useZeneraTranslation';

/**
 * Buyer Footer Component
 * Footer cho buyer layout
 */
export const BuyerFooter: React.FC = () => {
  const { t } = useZeneraTranslation('ecommerce');

  const footerSections = [
    {
      title: t('quickLinks'),
      links: [
        { href: '/about', label: t('about') },
        { href: '/contact', label: t('contact') },
        { href: '/help', label: t('help') },
      ],
    },
    {
      title: t('categories'),
      links: [
        { href: '/categories/electronics', label: t('electronics') },
        { href: '/categories/fashion', label: t('fashion') },
        { href: '/categories/home', label: t('homeGarden') },
      ],
    },
    {
      title: t('support'),
      links: [
        { href: '/shipping', label: t('shipping') },
        { href: '/returns', label: t('returns') },
        { href: '/privacy', label: t('privacy') },
      ],
    },
  ];

  return (
    <footer className="border-t bg-muted/50">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div>
            <h3 className="font-semibold mb-4">Zenera</h3>
            <p className="text-sm text-muted-foreground">
              {t('footerDescription')}
            </p>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <div key={index}>
              <h4 className="font-medium mb-4">{section.title}</h4>
              <ul className="space-y-2 text-sm">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link 
                      href={link.href} 
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Copyright */}
        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 Zenera. {t('allRightsReserved')}</p>
        </div>
      </div>
    </footer>
  );
};
