"use client";

import React from 'react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { 
  Trash2, 
  Plus, 
  Minus, 
  ShoppingBag,
  ArrowLeft,
  Heart,
  Gift,
  Truck,
  Tag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useHTranslation } from '@/lib/i18n/hooks/useHTranslation';
import { useCartStore } from '@/lib/stores/cart-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useToast } from '@/hooks/use-toast';

/**
 * Shopping Cart Component
 * Trang giỏ hàng với quản lý sản phẩm và checkout
 * Tham khảo từ Medoo cart patterns và ZenBuyFE
 */
export const ShoppingCart: React.FC = () => {
  const router = useRouter();
  const { t } = useHTranslation('ecommerce');
  const { toast } = useToast();
  
  // Stores
  const { 
    items, 
    summary, 
    updateQuantity, 
    removeItem, 
    clearCart,
    isLoading 
  } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  // State
  const [promoCode, setPromoCode] = useState('');
  const [isPromoApplied, setIsPromoApplied] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>(
    items.map(item => item.id)
  );

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    try {
      await updateQuantity(itemId, newQuantity);
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToUpdateQuantity'),
        variant: "destructive",
      });
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeItem(itemId);
      toast({
        title: t('success'),
        description: t('itemRemoved'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToRemoveItem'),
        variant: "destructive",
      });
    }
  };

  const handleApplyPromo = () => {
    // TODO: Implement promo code validation
    if (promoCode.toLowerCase() === 'save10') {
      setIsPromoApplied(true);
      toast({
        title: t('success'),
        description: t('promoCodeApplied'),
      });
    } else {
      toast({
        title: t('error'),
        description: t('invalidPromoCode'),
        variant: "destructive",
      });
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    setSelectedItems(prev => 
      checked 
        ? [...prev, itemId]
        : prev.filter(id => id !== itemId)
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedItems(checked ? items.map(item => item.id) : []);
  };

  const handleCheckout = () => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/checkout');
      return;
    }
    
    if (selectedItems.length === 0) {
      toast({
        title: t('error'),
        description: t('selectItemsToCheckout'),
        variant: "destructive",
      });
      return;
    }
    
    router.push('/checkout');
  };

  const selectedItemsTotal = items
    .filter(item => selectedItems.includes(item.id))
    .reduce((total, item) => total + (item.price * item.quantity), 0);

  const selectedItemsCount = items
    .filter(item => selectedItems.includes(item.id))
    .reduce((total, item) => total + item.quantity, 0);

  if (items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <ShoppingBag className="h-24 w-24 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">{t('emptyCart')}</h2>
          <p className="text-muted-foreground mb-6">{t('emptyCartDescription')}</p>
          <Button onClick={() => router.push('/products')}>
            {t('continueShopping')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('back')}
          </Button>
          <h1 className="text-3xl font-bold">{t('shoppingCart')}</h1>
          <Badge variant="secondary">
            {summary.totalItems} {t('items')}
          </Badge>
        </div>
        
        <Button variant="outline" onClick={clearCart}>
          <Trash2 className="h-4 w-4 mr-2" />
          {t('clearCart')}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {/* Select All */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={selectedItems.length === items.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="font-medium">
                  {t('selectAll')} ({items.length} {t('items')})
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Cart Items List */}
          {items.map((item) => (
            <Card key={item.id}>
              <CardContent className="pt-6">
                <div className="flex items-start space-x-4">
                  {/* Selection Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => 
                      handleSelectItem(item.id, checked as boolean)
                    }
                  />

                  {/* Product Image */}
                  <div className="relative w-24 h-24 flex-shrink-0">
                    <Image
                      src={item.image || '/placeholder-product.jpg'}
                      alt={item.name}
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-lg mb-1 truncate">
                      {item.name}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      {item.seller?.name}
                    </p>
                    
                    {/* Variant Info */}
                    {item.variant && (
                      <div className="flex space-x-2 mb-2">
                        {Object.entries(item.variant).map(([key, value]) => (
                          <Badge key={key} variant="outline" className="text-xs">
                            {key}: {value}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Price */}
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-lg font-bold">
                        ${item.price.toFixed(2)}
                      </span>
                      {item.originalPrice && item.originalPrice > item.price && (
                        <span className="text-sm text-muted-foreground line-through">
                          ${item.originalPrice.toFixed(2)}
                        </span>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between">
                      {/* Quantity Controls */}
                      <div className="flex items-center border rounded-md">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          disabled={item.quantity <= 1 || isLoading}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="px-3 py-1 min-w-[50px] text-center">
                          {item.quantity}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          disabled={isLoading}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Item Actions */}
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={isLoading}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Item Total */}
                  <div className="text-right">
                    <p className="text-lg font-bold">
                      ${(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          {/* Promo Code */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                {t('promoCode')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2">
                <Input
                  placeholder={t('enterPromoCode')}
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value)}
                  disabled={isPromoApplied}
                />
                <Button 
                  onClick={handleApplyPromo}
                  disabled={!promoCode || isPromoApplied}
                  variant="outline"
                >
                  {t('apply')}
                </Button>
              </div>
              {isPromoApplied && (
                <div className="mt-2 text-sm text-green-600">
                  {t('promoCodeApplied')}: SAVE10 (-10%)
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>{t('selectedItems')} ({selectedItemsCount})</span>
                <span>${selectedItemsTotal.toFixed(2)}</span>
              </div>
              
              <div className="flex justify-between">
                <span>{t('shipping')}</span>
                <span className="text-green-600">{t('free')}</span>
              </div>
              
              {isPromoApplied && (
                <div className="flex justify-between text-green-600">
                  <span>{t('discount')} (SAVE10)</span>
                  <span>-${(selectedItemsTotal * 0.1).toFixed(2)}</span>
                </div>
              )}
              
              <Separator />
              
              <div className="flex justify-between text-lg font-bold">
                <span>{t('total')}</span>
                <span>
                  ${(selectedItemsTotal * (isPromoApplied ? 0.9 : 1)).toFixed(2)}
                </span>
              </div>

              <Button 
                onClick={handleCheckout}
                className="w-full"
                size="lg"
                disabled={selectedItems.length === 0}
              >
                {t('proceedToCheckout')} ({selectedItemsCount})
              </Button>

              <Button 
                variant="outline" 
                onClick={() => router.push('/products')}
                className="w-full"
              >
                {t('continueShopping')}
              </Button>
            </CardContent>
          </Card>

          {/* Shipping Info */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Truck className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium text-sm">{t('freeShipping')}</p>
                  <p className="text-xs text-muted-foreground">
                    {t('freeShippingDescription')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Gift Option */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Gift className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium text-sm">{t('giftWrapping')}</p>
                  <p className="text-xs text-muted-foreground">
                    {t('giftWrappingDescription')}
                  </p>
                  <Button variant="link" className="p-0 h-auto text-xs mt-1">
                    {t('addGiftWrapping')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
