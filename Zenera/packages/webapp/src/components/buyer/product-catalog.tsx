"use client";

import React from 'react';
import { useState, useEffect } from 'react';
import { SlidersHorizontal } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useZeneraTranslation } from '@/lib/hooks/simple-translation';
import { useProductsStore } from '@/lib/stores/products-store';
import { ProductFilters } from './catalog/product-filters';
import { ProductGrid } from './catalog/product-grid';
import { CatalogHeader } from './catalog/catalog-header';

interface ProductCatalogProps {
  initialCategory?: string;
  initialSearch?: string;
}

interface FilterState {
  categories: string[];
  priceRange: [number, number];
  brands: string[];
  rating: number;
  inStock: boolean;
}

/**
 * Product Catalog Component
 * Trang danh sách sản phẩm với filtering và search
 * Đã được chia nhỏ theo pattern của Medoo
 */
export const ProductCatalog: React.FC<ProductCatalogProps> = ({
  initialCategory,
  initialSearch
}) => {
  const { t } = useZeneraTranslation('ecommerce');

  // Stores
  const {
    products,
    isLoading,
    fetchProducts,
    searchProducts,
    fetchCategories,
    categories,
    brands
  } = useProductsStore();

  // State
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [filters, setFilters] = useState<FilterState>({
    categories: initialCategory ? [initialCategory] : [],
    priceRange: [0, 1000],
    brands: [],
    rating: 0,
    inStock: false,
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Load products on mount and filter changes
  useEffect(() => {
    const loadProducts = async () => {
      // Load categories and brands first
      await fetchCategories();

      if (initialSearch) {
        await searchProducts(initialSearch);
      } else {
        await fetchProducts({
          category: filters.categories,
          priceMin: filters.priceRange[0],
          priceMax: filters.priceRange[1],
          brands: filters.brands,
          minRating: filters.rating,
          inStock: filters.inStock,
          sortBy,
        });
      }
    };

    loadProducts();
  }, [filters, sortBy, initialSearch]);

  const clearFilters = () => {
    setFilters({
      categories: [],
      priceRange: [0, 1000],
      brands: [],
      rating: 0,
      inStock: false,
    });
  };

  const title = initialSearch
    ? `${t('searchResults')}: "${initialSearch}"`
    : t('products');

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <CatalogHeader
        title={title}
        productCount={products.length}
        sortBy={sortBy}
        onSortChange={setSortBy}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        isFilterOpen={isFilterOpen}
        onFilterOpenChange={setIsFilterOpen}
        filterPanel={
          <ProductFilters
            filters={filters}
            onFiltersChange={setFilters}
            categories={categories}
            brands={brands}
          />
        }
      />

      <div className="flex gap-8">
        {/* Desktop Filters */}
        <aside className="hidden md:block w-64 flex-shrink-0">
          <Card>
            <CardHeader>
              <h2 className="font-semibold flex items-center">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                {t('filters')}
              </h2>
            </CardHeader>
            <CardContent>
              <ProductFilters
                filters={filters}
                onFiltersChange={setFilters}
                categories={categories}
                brands={brands}
              />
            </CardContent>
          </Card>
        </aside>

        {/* Products Grid */}
        <div className="flex-1">
          <ProductGrid
            products={products}
            isLoading={isLoading}
            viewMode={viewMode}
            onClearFilters={clearFilters}
          />
        </div>
      </div>
    </div>
  );
};
