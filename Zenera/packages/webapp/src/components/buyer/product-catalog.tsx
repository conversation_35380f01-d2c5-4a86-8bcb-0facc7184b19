"use client";

import React from 'react';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Filter, 
  Grid, 
  List, 
  SlidersHorizontal,
  ChevronDown,
  Star,
  Heart,
  ShoppingCart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { useHTranslation } from '@/lib/i18n/hooks/useHTranslation';
import { useCartStore } from '@/lib/stores/cart-store';
import { useProductsStore } from '@/lib/stores/products-store';
import { ProductCard } from '@/components/ui-components/src/components/compositions/ProductCard';

interface ProductCatalogProps {
  initialCategory?: string;
  initialSearch?: string;
}

interface FilterState {
  categories: string[];
  priceRange: [number, number];
  brands: string[];
  rating: number;
  inStock: boolean;
}

/**
 * Product Catalog Component
 * Trang danh sách sản phẩm với filtering và search
 * Tham khảo từ ZenBuyFE product listing patterns
 */
export const ProductCatalog: React.FC<ProductCatalogProps> = ({
  initialCategory,
  initialSearch
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useHTranslation('ecommerce');
  
  // Stores
  const { addItem: addToCart } = useCartStore();
  const { 
    products, 
    isLoading, 
    fetchProducts, 
    searchProducts,
    categories,
    brands 
  } = useProductsStore();

  // State
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [filters, setFilters] = useState<FilterState>({
    categories: initialCategory ? [initialCategory] : [],
    priceRange: [0, 1000],
    brands: [],
    rating: 0,
    inStock: false,
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Load products on mount and filter changes
  useEffect(() => {
    const loadProducts = async () => {
      if (initialSearch) {
        await searchProducts(initialSearch);
      } else {
        await fetchProducts({
          category: filters.categories,
          priceMin: filters.priceRange[0],
          priceMax: filters.priceRange[1],
          brands: filters.brands,
          minRating: filters.rating,
          inStock: filters.inStock,
          sortBy,
        });
      }
    };

    loadProducts();
  }, [filters, sortBy, initialSearch]);

  // Filter handlers
  const handleCategoryChange = (category: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      categories: checked 
        ? [...prev.categories, category]
        : prev.categories.filter(c => c !== category)
    }));
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      brands: checked 
        ? [...prev.brands, brand]
        : prev.brands.filter(b => b !== brand)
    }));
  };

  const handlePriceRangeChange = (value: [number, number]) => {
    setFilters(prev => ({ ...prev, priceRange: value }));
  };

  const handleAddToCart = async (product: any) => {
    try {
      await addToCart(product, 1);
      // Show success message
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  };

  const clearFilters = () => {
    setFilters({
      categories: [],
      priceRange: [0, 1000],
      brands: [],
      rating: 0,
      inStock: false,
    });
  };

  // Filter Panel Component
  const FilterPanel = () => (
    <div className="space-y-6">
      {/* Categories */}
      <div>
        <h3 className="font-medium mb-3">{t('categories')}</h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={filters.categories.includes(category.id)}
                onCheckedChange={(checked) => 
                  handleCategoryChange(category.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`category-${category.id}`}
                className="text-sm font-normal"
              >
                {category.name} ({category.productCount})
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Price Range */}
      <div>
        <h3 className="font-medium mb-3">{t('priceRange')}</h3>
        <div className="space-y-4">
          <Slider
            value={filters.priceRange}
            onValueChange={handlePriceRangeChange}
            max={1000}
            step={10}
            className="w-full"
          />
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>${filters.priceRange[0]}</span>
            <span>${filters.priceRange[1]}</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Brands */}
      <div>
        <h3 className="font-medium mb-3">{t('brands')}</h3>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center space-x-2">
              <Checkbox
                id={`brand-${brand.id}`}
                checked={filters.brands.includes(brand.id)}
                onCheckedChange={(checked) => 
                  handleBrandChange(brand.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`brand-${brand.id}`}
                className="text-sm font-normal"
              >
                {brand.name} ({brand.productCount})
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Rating */}
      <div>
        <h3 className="font-medium mb-3">{t('rating')}</h3>
        <div className="space-y-2">
          {[4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={filters.rating === rating}
                onCheckedChange={(checked) => 
                  setFilters(prev => ({ ...prev, rating: checked ? rating : 0 }))
                }
              />
              <Label htmlFor={`rating-${rating}`} className="flex items-center space-x-1">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm">& {t('up')}</span>
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Availability */}
      <div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="in-stock"
            checked={filters.inStock}
            onCheckedChange={(checked) => 
              setFilters(prev => ({ ...prev, inStock: checked as boolean }))
            }
          />
          <Label htmlFor="in-stock">{t('inStockOnly')}</Label>
        </div>
      </div>

      {/* Clear Filters */}
      <Button variant="outline" onClick={clearFilters} className="w-full">
        {t('clearFilters')}
      </Button>
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">
            {initialSearch ? `${t('searchResults')}: "${initialSearch}"` : t('products')}
          </h1>
          <p className="text-muted-foreground">
            {t('productsFound', { count: products.length })}
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          {/* Sort */}
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder={t('sortBy')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="featured">{t('featured')}</SelectItem>
              <SelectItem value="price-low">{t('priceLowToHigh')}</SelectItem>
              <SelectItem value="price-high">{t('priceHighToLow')}</SelectItem>
              <SelectItem value="newest">{t('newest')}</SelectItem>
              <SelectItem value="rating">{t('topRated')}</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Filter */}
          <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="md:hidden">
                <Filter className="h-4 w-4 mr-2" />
                {t('filters')}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <SheetHeader>
                <SheetTitle>{t('filters')}</SheetTitle>
                <SheetDescription>
                  {t('filterDescription')}
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6">
                <FilterPanel />
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      <div className="flex gap-8">
        {/* Desktop Filters */}
        <aside className="hidden md:block w-64 flex-shrink-0">
          <Card>
            <CardHeader>
              <h2 className="font-semibold flex items-center">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                {t('filters')}
              </h2>
            </CardHeader>
            <CardContent>
              <FilterPanel />
            </CardContent>
          </Card>
        </aside>

        {/* Products Grid */}
        <div className="flex-1">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">{t('noProductsFound')}</p>
              <Button variant="outline" onClick={clearFilters} className="mt-4">
                {t('clearFilters')}
              </Button>
            </div>
          ) : (
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  variant={viewMode === 'list' ? 'horizontal' : 'default'}
                  onAddToCart={() => handleAddToCart(product)}
                  onAddToWishlist={(id) => console.log('Add to wishlist:', id)}
                  onQuickView={(id) => router.push(`/products/${id}`)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
