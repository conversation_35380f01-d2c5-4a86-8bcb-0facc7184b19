"use client";

import React from 'react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { 
  Star, 
  Heart, 
  Share2, 
  ShoppingCart, 
  Plus, 
  Minus,
  Truck,
  Shield,
  RotateCcw,
  Check,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { useZeneraTranslation } from '@/lib/hooks/simple-translation';
import { useCartStore } from '@/lib/stores/cart-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useToast } from '@/hooks/use-toast';

interface ProductDetailProps {
  productId: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: {
    id: string;
    name: string;
  };
  brand: {
    id: string;
    name: string;
  };
  rating: number;
  reviewCount: number;
  stock: number;
  sku: string;
  specifications: Record<string, string>;
  features: string[];
  seller: {
    id: string;
    name: string;
    rating: number;
    avatar?: string;
  };
  reviews: Array<{
    id: string;
    user: {
      name: string;
      avatar?: string;
    };
    rating: number;
    comment: string;
    date: string;
    images?: string[];
  }>;
}

/**
 * Product Detail Component
 * Trang chi tiết sản phẩm với reviews và specifications
 * Tham khảo từ ZenBuyFE product detail patterns
 */
export const ProductDetail: React.FC<ProductDetailProps> = ({ productId }) => {
  const router = useRouter();
  const { t } = useZeneraTranslation('ecommerce');
  const { toast } = useToast();
  
  // Stores
  const { addItem: addToCart, isLoading: cartLoading } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  // State
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      try {
        setIsLoading(true);
        // TODO: Replace with actual API call
        const mockProduct: Product = {
          id: productId,
          name: "Premium Wireless Headphones",
          description: "High-quality wireless headphones with noise cancellation and premium sound quality. Perfect for music lovers and professionals.",
          price: 199.99,
          originalPrice: 249.99,
          images: [
            "/placeholder-product-1.jpg",
            "/placeholder-product-2.jpg",
            "/placeholder-product-3.jpg",
            "/placeholder-product-4.jpg"
          ],
          category: { id: "electronics", name: "Electronics" },
          brand: { id: "techbrand", name: "TechBrand" },
          rating: 4.5,
          reviewCount: 128,
          stock: 15,
          sku: "WH-1000XM4",
          specifications: {
            "Driver Size": "40mm",
            "Frequency Response": "4Hz-40kHz",
            "Battery Life": "30 hours",
            "Charging Time": "3 hours",
            "Weight": "254g",
            "Connectivity": "Bluetooth 5.0, 3.5mm jack"
          },
          features: [
            "Active Noise Cancellation",
            "30-hour battery life",
            "Quick charge (10min = 5hrs)",
            "Touch controls",
            "Voice assistant compatible",
            "Foldable design"
          ],
          seller: {
            id: "seller1",
            name: "TechStore Official",
            rating: 4.8,
            avatar: "/placeholder-seller.jpg"
          },
          reviews: [
            {
              id: "1",
              user: { name: "John Doe", avatar: "/placeholder-user.jpg" },
              rating: 5,
              comment: "Excellent sound quality and comfortable to wear for long periods.",
              date: "2024-01-15",
              images: ["/placeholder-review-1.jpg"]
            },
            {
              id: "2",
              user: { name: "Jane Smith" },
              rating: 4,
              comment: "Great headphones, but the price is a bit high.",
              date: "2024-01-10"
            }
          ]
        };
        
        setProduct(mockProduct);
      } catch (error) {
        console.error('Failed to load product:', error);
        toast({
          title: t('error'),
          description: t('failedToLoadProduct'),
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProduct();
  }, [productId]);

  const handleAddToCart = async () => {
    if (!product) return;
    
    try {
      await addToCart(product, quantity);
      toast({
        title: t('success'),
        description: t('addedToCart'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failedToAddToCart'),
        variant: "destructive",
      });
    }
  };

  const handleBuyNow = async () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }
    
    await handleAddToCart();
    router.push('/checkout');
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    toast({
      title: isWishlisted ? t('removedFromWishlist') : t('addedToWishlist'),
      description: product?.name,
    });
  };

  const discountPercentage = product?.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div className="flex space-x-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="w-20 h-20 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-6 bg-gray-200 rounded w-2/3"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">{t('productNotFound')}</h1>
        <Button onClick={() => router.back()}>{t('goBack')}</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
        <button onClick={() => router.push('/')} className="hover:text-foreground">
          {t('home')}
        </button>
        <span>/</span>
        <button 
          onClick={() => router.push(`/categories/${product.category.id}`)}
          className="hover:text-foreground"
        >
          {product.category.name}
        </button>
        <span>/</span>
        <span className="text-foreground">{product.name}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
            <Image
              src={product.images[selectedImageIndex]}
              alt={product.name}
              fill
              className="object-cover"
              priority
            />
            
            {/* Image Navigation */}
            {product.images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                  onClick={() => setSelectedImageIndex(
                    selectedImageIndex === 0 ? product.images.length - 1 : selectedImageIndex - 1
                  )}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                  onClick={() => setSelectedImageIndex(
                    selectedImageIndex === product.images.length - 1 ? 0 : selectedImageIndex + 1
                  )}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>

          {/* Thumbnail Images */}
          <div className="flex space-x-2 overflow-x-auto">
            {product.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImageIndex(index)}
                className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                  selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                }`}
              >
                <Image
                  src={image}
                  alt={`${product.name} ${index + 1}`}
                  width={80}
                  height={80}
                  className="object-cover w-full h-full"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          {/* Title and Rating */}
          <div>
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center space-x-1">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(product.rating) 
                          ? 'fill-yellow-400 text-yellow-400' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  {product.rating} ({product.reviewCount} {t('reviews')})
                </span>
              </div>
              <Badge variant="secondary">SKU: {product.sku}</Badge>
            </div>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-4">
            <span className="text-3xl font-bold">${product.price}</span>
            {product.originalPrice && (
              <>
                <span className="text-xl text-muted-foreground line-through">
                  ${product.originalPrice}
                </span>
                <Badge variant="destructive">-{discountPercentage}%</Badge>
              </>
            )}
          </div>

          {/* Stock Status */}
          <div className="flex items-center space-x-2">
            {product.stock > 0 ? (
              <>
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-green-600">
                  {t('inStock')} ({product.stock} {t('available')})
                </span>
              </>
            ) : (
              <span className="text-red-600">{t('outOfStock')}</span>
            )}
          </div>

          {/* Description */}
          <p className="text-muted-foreground">{product.description}</p>

          {/* Quantity and Actions */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <label className="font-medium">{t('quantity')}:</label>
              <div className="flex items-center border rounded-md">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="px-4 py-2 min-w-[60px] text-center">{quantity}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                  disabled={quantity >= product.stock}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button
                onClick={handleAddToCart}
                disabled={product.stock === 0 || cartLoading}
                className="flex-1"
                variant="outline"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {t('addToCart')}
              </Button>
              <Button
                onClick={handleBuyNow}
                disabled={product.stock === 0}
                className="flex-1"
              >
                {t('buyNow')}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleWishlist}
              >
                <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-red-500 text-red-500' : ''}`} />
              </Button>
              <Button variant="outline" size="icon">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Features */}
          <div>
            <h3 className="font-semibold mb-3">{t('keyFeatures')}</h3>
            <ul className="space-y-2">
              {product.features.map((feature, index) => (
                <li key={index} className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Seller Info */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src={product.seller.avatar} />
                  <AvatarFallback>{product.seller.name[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h4 className="font-medium">{product.seller.name}</h4>
                  <div className="flex items-center space-x-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-muted-foreground">
                      {product.seller.rating} {t('sellerRating')}
                    </span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  {t('viewStore')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Shipping & Returns */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <Truck className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium text-sm">{t('freeShipping')}</p>
                <p className="text-xs text-muted-foreground">{t('ordersOver50')}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <RotateCcw className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium text-sm">{t('easyReturns')}</p>
                <p className="text-xs text-muted-foreground">{t('30DayReturn')}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <Shield className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium text-sm">{t('warranty')}</p>
                <p className="text-xs text-muted-foreground">{t('1YearWarranty')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <Tabs defaultValue="specifications" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="specifications">{t('specifications')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('reviews')}</TabsTrigger>
          <TabsTrigger value="shipping">{t('shipping')}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="specifications" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('productSpecifications')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b">
                    <span className="font-medium">{key}:</span>
                    <span className="text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reviews" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('customerReviews')}</CardTitle>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl font-bold">{product.rating}</span>
                  <div className="flex">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(product.rating) 
                            ? 'fill-yellow-400 text-yellow-400' 
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-muted-foreground">
                    ({product.reviewCount} {t('reviews')})
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {product.reviews.map((review) => (
                  <div key={review.id} className="border-b pb-6 last:border-b-0">
                    <div className="flex items-start space-x-4">
                      <Avatar>
                        <AvatarImage src={review.user.avatar} />
                        <AvatarFallback>{review.user.name[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-medium">{review.user.name}</span>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < review.rating 
                                    ? 'fill-yellow-400 text-yellow-400' 
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.date).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-muted-foreground mb-3">{review.comment}</p>
                        {review.images && (
                          <div className="flex space-x-2">
                            {review.images.map((image, index) => (
                              <Image
                                key={index}
                                src={image}
                                alt={`Review image ${index + 1}`}
                                width={80}
                                height={80}
                                className="rounded-lg object-cover"
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="shipping" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('shippingInformation')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">{t('shippingOptions')}</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• {t('standardShipping')}: 3-5 {t('businessDays')} - {t('free')}</li>
                    <li>• {t('expressShipping')}: 1-2 {t('businessDays')} - $9.99</li>
                    <li>• {t('overnightShipping')}: {t('nextBusinessDay')} - $19.99</li>
                  </ul>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium mb-2">{t('returnPolicy')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('returnPolicyDescription')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
