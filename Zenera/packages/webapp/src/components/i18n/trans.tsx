"use client";

import { Trans as ReactI18nextTrans } from "react-i18next";
import { useZeneraTranslation } from "@/lib/hooks/simple-translation";

/**
 * Enhanced Trans component
 * Inspired by Medoo's Trans usage patterns
 */

export interface ZeneraTransProps {
  i18nKey: string;
  namespace?: string;
  values?: Record<string, any>;
  components?: Record<string, React.ReactElement>;
  defaults?: string;
  shouldUnescape?: boolean;
  parent?: string | React.ComponentType<any> | null;
  tOptions?: any;
  children?: React.ReactNode;
}

export function ZeneraTrans({
  i18nKey,
  namespace = "translation",
  values = {},
  components = {
    b: <b />,
    br: <br />,
    strong: <strong />,
    em: <em />,
    i: <i />,
    u: <u />,
    span: <span />,
    div: <div />,
    p: <p />,
  },
  defaults,
  shouldUnescape = false,
  parent = null,
  tOptions = {},
  children,
}: ZeneraTransProps) {
  const { t } = useZeneraTranslation(namespace);

  return (
    <ReactI18nextTrans
      i18nKey={i18nKey}
      t={t}
      values={values}
      components={components}
      defaults={defaults}
      shouldUnescape={shouldUnescape}
      parent={parent}
      tOptions={tOptions}
    >
      {children}
    </ReactI18nextTrans>
  );
}

/**
 * Hook for getting translated content with Trans support
 * Inspired by Medoo's useGetTranslatedNotification
 */
export const useGetTranslatedContent = (namespace: string = "translation") => {
  const { t } = useZeneraTranslation(namespace);

  return (contentData: any) => {
    const { code, data = {} } = contentData;
    
    // Clone and process data
    const mappedData = { ...data };
    
    // Process multi-language content in data
    Object.keys(mappedData).forEach(key => {
      if (typeof mappedData[key] === "object" && mappedData[key] !== null) {
        // Handle multi-language content
        const locale = t.i18n?.language || "en";
        mappedData[key] = mappedData[key][locale] || mappedData[key]["en"] || mappedData[key];
      }
    });

    return (
      <ZeneraTrans
        i18nKey={code}
        namespace={namespace}
        values={mappedData}
        components={{
          b: <b />,
          br: <br />,
          strong: <strong />,
          em: <em />,
          i: <i />,
          u: <u />,
          span: <span />,
          link: <a href="#" className="text-blue-600 hover:text-blue-800" />,
        }}
      />
    );
  };
};

/**
 * Simple Trans wrapper for common use cases
 */
export function SimpleTrans({
  children,
  namespace = "translation",
  values = {},
}: {
  children: string;
  namespace?: string;
  values?: Record<string, any>;
}) {
  return (
    <ZeneraTrans
      i18nKey={children}
      namespace={namespace}
      values={values}
    />
  );
}

/**
 * Trans with rich text support
 */
export function RichTrans({
  i18nKey,
  namespace = "translation",
  values = {},
  className = "",
}: {
  i18nKey: string;
  namespace?: string;
  values?: Record<string, any>;
  className?: string;
}) {
  return (
    <div className={className}>
      <ZeneraTrans
        i18nKey={i18nKey}
        namespace={namespace}
        values={values}
        components={{
          b: <b className="font-bold" />,
          strong: <strong className="font-bold" />,
          em: <em className="italic" />,
          i: <i className="italic" />,
          u: <u className="underline" />,
          br: <br />,
          p: <p className="mb-2" />,
          span: <span />,
          link: <a className="text-blue-600 hover:text-blue-800 underline" />,
          highlight: <span className="bg-yellow-200 px-1 rounded" />,
          code: <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" />,
        }}
      />
    </div>
  );
}

/**
 * Export Trans as default
 */
export default ZeneraTrans;
