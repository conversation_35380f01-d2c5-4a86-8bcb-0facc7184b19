"use client";

import { useState } from "react";
import { useLocaleSwitch } from "@/lib/hooks/use-locale";
import { useZeneraTranslation } from "@/lib/hooks/simple-translation";
import { Languages, MAP_LANGUAGE_TEXT, LANGUAGE_CONFIGS } from "@/lib/constants/languages";
import { cn } from "@/lib/utils";

// Simple SVG icons
const ChevronDownIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const GlobeIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
  </svg>
);

/**
 * Language Switcher Component
 * Inspired by Medoo's language switcher
 */

export interface LanguageSwitcherProps {
  variant?: "dropdown" | "buttons" | "minimal";
  size?: "sm" | "md" | "lg";
  showFlag?: boolean;
  showText?: boolean;
  className?: string;
}

export function LanguageSwitcher({
  variant = "dropdown",
  size = "md",
  showFlag = true,
  showText = true,
  className,
}: LanguageSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { currentLocale, switchLocale, availableLocales } = useLocaleSwitch();
  const { t } = useZeneraTranslation();

  const currentConfig = LANGUAGE_CONFIGS[currentLocale as Languages];

  const sizeClasses = {
    sm: "text-sm px-2 py-1",
    md: "text-base px-3 py-2",
    lg: "text-lg px-4 py-3",
  };

  const handleLanguageChange = (locale: string) => {
    switchLocale(locale);
    setIsOpen(false);
  };

  if (variant === "buttons") {
    return (
      <div className={cn("flex space-x-2", className)}>
        {availableLocales.map((locale) => {
          const config = LANGUAGE_CONFIGS[locale];
          const isActive = locale === currentLocale;
          
          return (
            <button
              key={locale}
              onClick={() => handleLanguageChange(locale)}
              className={cn(
                "flex items-center space-x-2 rounded-md border transition-colors",
                sizeClasses[size],
                isActive
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              )}
            >
              {showFlag && <span className="text-lg">{config.flag}</span>}
              {showText && <span>{config.nativeName}</span>}
            </button>
          );
        })}
      </div>
    );
  }

  if (variant === "minimal") {
    return (
      <button
        onClick={() => {
          const nextLocale = currentLocale === Languages.EN ? Languages.VI : Languages.EN;
          handleLanguageChange(nextLocale);
        }}
        className={cn(
          "flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",
          sizeClasses[size],
          className
        )}
        title={t("language.change")}
      >
        {showFlag && <span className="text-lg">{currentConfig.flag}</span>}
        {showText && <span>{currentConfig.code.toUpperCase()}</span>}
      </button>
    );
  }

  // Default dropdown variant
  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center space-x-2 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
          sizeClasses[size]
        )}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <GlobeIcon className="h-4 w-4 text-gray-500" />
        {showFlag && <span className="text-lg">{currentConfig.flag}</span>}
        {showText && <span className="text-gray-700">{currentConfig.nativeName}</span>}
        <ChevronDownIcon 
          className={cn(
            "h-4 w-4 text-gray-500 transition-transform",
            isOpen && "rotate-180"
          )} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-20">
            <div className="py-1">
              {availableLocales.map((locale) => {
                const config = LANGUAGE_CONFIGS[locale];
                const isActive = locale === currentLocale;
                
                return (
                  <button
                    key={locale}
                    onClick={() => handleLanguageChange(locale)}
                    className={cn(
                      "w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors",
                      isActive && "bg-blue-50 text-blue-600"
                    )}
                  >
                    <span className="text-lg">{config.flag}</span>
                    <div className="flex-1">
                      <div className="font-medium">{config.nativeName}</div>
                      <div className="text-sm text-gray-500">{config.name}</div>
                    </div>
                    {isActive && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

/**
 * Compact Language Switcher for mobile
 */
export function CompactLanguageSwitcher({ className }: { className?: string }) {
  const { currentLocale, switchLocale } = useLocaleSwitch();
  const currentConfig = LANGUAGE_CONFIGS[currentLocale as Languages];

  const handleToggle = () => {
    const nextLocale = currentLocale === Languages.EN ? Languages.VI : Languages.EN;
    switchLocale(nextLocale);
  };

  return (
    <button
      onClick={handleToggle}
      className={cn(
        "flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",
        className
      )}
      title="Switch Language"
    >
      <span className="text-lg">{currentConfig.flag}</span>
    </button>
  );
}

/**
 * Language Switcher for forms
 */
export function FormLanguageSwitcher({ 
  value, 
  onChange, 
  className 
}: { 
  value: string;
  onChange: (locale: string) => void;
  className?: string;
}) {
  return (
    <div className={cn("flex space-x-1 p-1 bg-gray-100 rounded-lg", className)}>
      {Object.values(Languages).map((locale) => {
        const config = LANGUAGE_CONFIGS[locale];
        const isActive = locale === value;
        
        return (
          <button
            key={locale}
            type="button"
            onClick={() => onChange(locale)}
            className={cn(
              "flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",
              isActive
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            )}
          >
            <span>{config.flag}</span>
            <span>{config.code.toUpperCase()}</span>
          </button>
        );
      })}
    </div>
  );
}

export default LanguageSwitcher;
