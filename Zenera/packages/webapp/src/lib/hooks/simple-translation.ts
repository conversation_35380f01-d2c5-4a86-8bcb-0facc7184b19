// Simple translation hook for development
export const useZeneraTranslation = (namespace?: string) => {
  const t = (key: string, options?: any) => {
    // Simple mock translations
    const translations: Record<string, string> = {
      // Navigation
      'home': 'Home',
      'products': 'Products',
      'categories': 'Categories',
      'deals': 'Deals',
      'cart': 'Cart',
      'wishlist': 'Wishlist',
      'profile': 'Profile',
      'orders': 'Orders',
      'addresses': 'Addresses',
      'settings': 'Settings',
      'login': 'Login',
      'register': 'Register',
      'logout': 'Logout',
      
      // Search & Filters
      'searchProducts': 'Search products...',
      'filters': 'Filters',
      'filterDescription': 'Refine your search results',
      'priceRange': 'Price Range',
      'brands': 'Brands',
      'rating': 'Rating',
      'up': 'up',
      'inStockOnly': 'In stock only',
      'clearFilters': 'Clear Filters',
      'sortBy': 'Sort by',
      'featured': 'Featured',
      'priceLowToHigh': 'Price: Low to High',
      'priceHighToLow': 'Price: High to Low',
      'newest': 'Newest',
      'topRated': 'Top Rated',
      
      // Product
      'productsFound': `{{count}} products found`,
      'noProductsFound': 'No products found',
      'addToCart': 'Add to Cart',
      'addedToCart': 'Added to cart',
      'buyNow': 'Buy Now',
      'inStock': 'In Stock',
      'outOfStock': 'Out of Stock',
      'available': 'available',
      'quantity': 'Quantity',
      'reviews': 'reviews',
      
      // Footer
      'footerDescription': 'Your trusted e-commerce platform for quality products and exceptional service.',
      'quickLinks': 'Quick Links',
      'about': 'About',
      'contact': 'Contact',
      'help': 'Help',
      'electronics': 'Electronics',
      'fashion': 'Fashion',
      'homeGarden': 'Home & Garden',
      'sports': 'Sports',
      'books': 'Books',
      'beauty': 'Beauty',
      'support': 'Support',
      'shipping': 'Shipping',
      'returns': 'Returns',
      'privacy': 'Privacy',
      'allRightsReserved': 'All rights reserved.',
      
      // Home page
      'welcomeToZenera': 'Welcome to Zenera',
      'heroDescription': 'Discover amazing products from trusted sellers worldwide. Shop with confidence and enjoy fast, secure delivery.',
      'shopNow': 'Shop Now',
      'browseCategories': 'Browse Categories',
      'freeShipping': 'Free Shipping',
      'freeShippingDescription': 'Free shipping on orders over $50',
      'securePayment': 'Secure Payment',
      'securePaymentDescription': 'Your payment information is safe with us',
      'fastDelivery': 'Fast Delivery',
      'fastDeliveryDescription': 'Quick delivery to your doorstep',
      'shopByCategory': 'Shop by Category',
      'viewAll': 'View All',
      'items': 'items',
      'featuredProducts': 'Featured Products',
      'happyCustomers': 'Happy Customers',
      'averageRating': 'Average Rating',
      'growth': 'Growth',
      'stayUpdated': 'Stay Updated',
      'newsletterDescription': 'Get the latest deals and product updates delivered to your inbox.',
      'enterEmail': 'Enter your email',
      'subscribe': 'Subscribe',
      
      // Error states
      'error': 'Error',
      'success': 'Success',
      'failedToLoadProduct': 'Failed to load product',
      'failedToAddToCart': 'Failed to add to cart',
    };
    
    // Handle interpolation like {{count}}
    let result = translations[key] || key;
    if (options && typeof options === 'object') {
      Object.keys(options).forEach(optionKey => {
        result = result.replace(`{{${optionKey}}}`, options[optionKey]);
      });
    }
    
    return result;
  };
  
  return { t };
};
