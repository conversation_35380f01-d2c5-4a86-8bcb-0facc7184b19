"use client";

import React from 'react';
import Link from 'next/link';
import { 
  ShoppingBag, 
  Star, 
  TrendingUp, 
  Users,
  ArrowRight,
  Zap,
  Shield,
  Truck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useZeneraTranslation } from '@/lib/hooks/simple-translation';
import { ProductCard } from '@/components/ui/product-card';

/**
 * Buyer Home Page
 * Trang chủ cho buyer với featured products, categories, deals
 */
export default function BuyerHomePage() {
  const { t } = useZeneraTranslation('ecommerce');

  // Mock data
  const featuredProducts = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      price: 199.99,
      originalPrice: 249.99,
      image: '/placeholder-product-1.jpg',
      rating: 4.5,
      reviewCount: 128,
      seller: { name: 'TechStore' },
      isAvailable: true,
    },
    {
      id: '2',
      name: 'Smart Watch Pro',
      price: 299.99,
      originalPrice: 399.99,
      image: '/placeholder-product-2.jpg',
      rating: 4.8,
      reviewCount: 89,
      seller: { name: 'GadgetHub' },
      isAvailable: true,
    },
    {
      id: '3',
      name: 'Bluetooth Speaker',
      price: 79.99,
      originalPrice: 99.99,
      image: '/placeholder-product-3.jpg',
      rating: 4.3,
      reviewCount: 156,
      seller: { name: 'AudioWorld' },
      isAvailable: true,
    },
    {
      id: '4',
      name: 'Gaming Mouse',
      price: 59.99,
      image: '/placeholder-product-4.jpg',
      rating: 4.6,
      reviewCount: 203,
      seller: { name: 'GameGear' },
      isAvailable: true,
    },
  ];

  const categories = [
    { id: 'electronics', name: t('electronics'), icon: '📱', count: 1234 },
    { id: 'fashion', name: t('fashion'), icon: '👕', count: 856 },
    { id: 'home', name: t('home'), icon: '🏠', count: 642 },
    { id: 'sports', name: t('sports'), icon: '⚽', count: 423 },
    { id: 'books', name: t('books'), icon: '📚', count: 789 },
    { id: 'beauty', name: t('beauty'), icon: '💄', count: 567 },
  ];

  const stats = [
    { icon: Users, label: t('happyCustomers'), value: '50K+' },
    { icon: ShoppingBag, label: t('products'), value: '10K+' },
    { icon: Star, label: t('averageRating'), value: '4.8' },
    { icon: TrendingUp, label: t('growth'), value: '200%' },
  ];

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary/10 to-primary/5 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('welcomeToZenera')}
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              {t('heroDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/products">
                  {t('shopNow')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/categories">
                  {t('browseCategories')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Truck className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">{t('freeShipping')}</h3>
            <p className="text-muted-foreground">{t('freeShippingDescription')}</p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">{t('securePayment')}</h3>
            <p className="text-muted-foreground">{t('securePaymentDescription')}</p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">{t('fastDelivery')}</h3>
            <p className="text-muted-foreground">{t('fastDeliveryDescription')}</p>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">{t('shopByCategory')}</h2>
          <Button variant="outline" asChild>
            <Link href="/categories">
              {t('viewAll')}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <Card key={category.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-4xl mb-3">{category.icon}</div>
                <h3 className="font-semibold mb-1">{category.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {category.count} {t('items')}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Featured Products */}
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">{t('featuredProducts')}</h2>
          <Button variant="outline" asChild>
            <Link href="/products?featured=true">
              {t('viewAll')}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onAddToCart={(id) => console.log('Add to cart:', id)}
              onAddToWishlist={(id) => console.log('Add to wishlist:', id)}
              onQuickView={(id) => console.log('Quick view:', id)}
            />
          ))}
        </div>
      </section>

      {/* Stats */}
      <section className="bg-muted/50 py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <Icon className="h-8 w-8 mx-auto mb-4 text-primary" />
                  <div className="text-3xl font-bold mb-2">{stat.value}</div>
                  <div className="text-muted-foreground">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="container mx-auto px-4">
        <Card className="bg-primary text-primary-foreground">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">{t('stayUpdated')}</h2>
            <p className="mb-6 opacity-90">{t('newsletterDescription')}</p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder={t('enterEmail')}
                className="flex-1 px-4 py-2 rounded-md text-foreground"
              />
              <Button variant="secondary">
                {t('subscribe')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
