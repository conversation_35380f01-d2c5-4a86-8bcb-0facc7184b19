{"app": {"name": "Zenera", "description": "Modern E-commerce Platform", "welcome": "Welcome to Zenera", "loading": "Loading...", "error": "An error occurred", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"home": "Home", "products": "Products", "categories": "Categories", "cart": "<PERSON><PERSON>", "account": "Account", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "dashboard": "Dashboard", "orders": "Orders", "profile": "Profile", "settings": "Settings", "help": "Help", "contact": "Contact", "about": "About"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "update": "Update", "create": "Create", "submit": "Submit", "reset": "Reset", "clear": "Clear", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "copy": "Copy", "share": "Share", "print": "Print", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "close": "Close", "open": "Open", "expand": "Expand", "collapse": "Collapse", "select": "Select", "deselect": "Deselect", "confirm": "Confirm", "approve": "Approve", "reject": "Reject", "send": "Send", "receive": "Receive"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "returned": "Returned", "refunded": "Refunded", "draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "ago": "ago", "in": "in"}, "messages": {"noData": "No data available", "noResults": "No results found", "emptyList": "List is empty", "loadingData": "Loading data...", "savingData": "Saving data...", "dataUpdated": "Data updated successfully", "dataSaved": "Data saved successfully", "dataDeleted": "Data deleted successfully", "operationCompleted": "Operation completed successfully", "operationFailed": "Operation failed", "confirmDelete": "Are you sure you want to delete this item?", "confirmAction": "Are you sure you want to perform this action?", "unsavedChanges": "You have unsaved changes. Do you want to save them?", "sessionExpired": "Your session has expired. Please login again.", "accessDenied": "Access denied. You don't have permission to perform this action.", "networkError": "Network error. Please check your connection and try again.", "serverError": "Server error. Please try again later.", "validationError": "Please check the form data and try again.", "fileUploadError": "File upload failed. Please try again.", "fileTooLarge": "File is too large. Maximum size is {{maxSize}}.", "invalidFileType": "Invalid file type. Allowed types: {{allowedTypes}}.", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least {{minLength}} characters", "passwordMismatch": "Passwords do not match"}, "language": {"current": "Current language", "select": "Select language", "english": "English", "vietnamese": "Vietnamese", "change": "Change language", "changed": "Language changed to {{language}}"}, "currency": {"vnd": "VND", "usd": "USD", "eur": "EUR", "symbol": {"vnd": "₫", "usd": "$", "eur": "€"}}, "units": {"piece": "piece", "pieces": "pieces", "item": "item", "items": "items", "kg": "kg", "gram": "gram", "liter": "liter", "meter": "meter", "cm": "cm", "mm": "mm"}, "ecommerce": {"home": "Home", "products": "Products", "categories": "Categories", "deals": "Deals", "cart": {"emptyCart": "Your cart is empty", "emptyCartDescription": "Looks like you haven't added anything to your cart yet.", "continueShopping": "Continue Shopping", "back": "Back", "shoppingCart": "Shopping Cart", "clearCart": "Clear Cart", "selectAll": "Select All", "itemRemoved": "Item removed from cart", "failedToUpdateQuantity": "Failed to update quantity", "failedToRemoveItem": "Failed to remove item", "promoCode": "Promo Code", "enterPromoCode": "Enter promo code", "apply": "Apply", "promoCodeApplied": "Promo code applied successfully", "invalidPromoCode": "Invalid promo code", "orderSummary": "Order Summary", "selectedItems": "Selected items", "discount": "Discount", "total": "Total", "proceedToCheckout": "Proceed to Checkout", "selectItemsToCheckout": "Please select items to checkout", "giftWrapping": "Gift Wrapping", "giftWrappingDescription": "Make your gift extra special", "addGiftWrapping": "Add Gift Wrapping"}, "wishlist": "Wishlist", "profile": {"pleaseLogin": "Please login to view your profile", "personalInformation": "Personal Information", "edit": "Edit", "cancel": "Cancel", "saveChanges": "Save Changes", "profileUpdated": "Profile updated successfully", "failedToUpdateProfile": "Failed to update profile", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "dateOfBirth": "Date of Birth", "bio": "Bio", "memberSince": "Member since", "verified": "Verified", "shippingAddresses": "Shipping Addresses", "addAddress": "Add Address", "addNewAddress": "Add New Address", "editAddress": "Edit Address", "fullName": "Full Name", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2 (Optional)", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "setAsDefault": "Set as default address", "saveAddress": "Save Address", "addressSaved": "Address saved successfully", "failedToSaveAddress": "Failed to save address", "addressDeleted": "Address deleted successfully", "failedToDeleteAddress": "Failed to delete address", "default": "<PERSON><PERSON><PERSON>", "orderHistory": "Order History", "noOrdersYet": "No orders yet", "startShopping": "Start Shopping", "noWishlistItems": "No items in your wishlist", "browseProducts": "Browse Products", "notifications": "Notifications", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive updates via email", "orderUpdates": "Order Updates", "orderUpdatesDescription": "Get notified about your order status", "promotionalEmails": "Promotional Emails", "promotionalEmailsDescription": "Receive deals and offers", "security": "Security", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "loginHistory": "Login History", "paymentMethods": "Payment Methods", "noPaymentMethods": "No payment methods added", "addPaymentMethod": "Add Payment Method"}, "orders": "Orders", "addresses": "Addresses", "settings": "Settings", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "searchProducts": "Search products...", "footerDescription": "Your trusted e-commerce platform for quality products and exceptional service.", "quickLinks": "Quick Links", "about": "About", "contact": "Contact", "help": "Help", "electronics": "Electronics", "fashion": "Fashion", "homeGarden": "Home & Garden", "sports": "Sports", "books": "Books", "beauty": "Beauty", "support": "Support", "shipping": "Shipping", "returns": "Returns", "privacy": "Privacy", "allRightsReserved": "All rights reserved.", "welcomeToZenera": "Welcome to Zenera", "heroDescription": "Discover amazing products from trusted sellers worldwide. Shop with confidence and enjoy fast, secure delivery.", "shopNow": "Shop Now", "browseCategories": "Browse Categories", "freeShipping": "Free Shipping", "freeShippingDescription": "Free shipping on orders over $50", "securePayment": "Secure Payment", "securePaymentDescription": "Your payment information is safe with us", "fastDelivery": "Fast Delivery", "fastDeliveryDescription": "Quick delivery to your doorstep", "shopByCategory": "Shop by Category", "viewAll": "View All", "items": "items", "featuredProducts": "Featured Products", "happyCustomers": "Happy Customers", "averageRating": "Average Rating", "growth": "Growth", "stayUpdated": "Stay Updated", "newsletterDescription": "Get the latest deals and product updates delivered to your inbox.", "enterEmail": "Enter your email", "subscribe": "Subscribe", "productsFound": "{{count}} products found", "sortBy": "Sort by", "featured": "Featured", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "newest": "Newest", "topRated": "Top Rated", "filters": "Filters", "filterDescription": "Refine your search results", "priceRange": "Price Range", "brands": "Brands", "rating": "Rating", "up": "up", "inStockOnly": "In stock only", "clearFilters": "Clear Filters", "noProductsFound": "No products found", "addToCart": "Add to Cart", "addedToCart": "Added to cart", "errorAddingToCart": "Error adding to cart", "productNotFound": "Product not found", "goBack": "Go Back", "reviews": "reviews", "inStock": "In Stock", "available": "available", "outOfStock": "Out of Stock", "quantity": "Quantity", "buyNow": "Buy Now", "keyFeatures": "Key Features", "sellerRating": "seller rating", "viewStore": "View Store", "ordersOver50": "on orders over $50", "easyReturns": "Easy Returns", "30DayReturn": "30-day return policy", "warranty": "Warranty", "1YearWarranty": "1-year warranty", "specifications": "Specifications", "shippingInformation": "Shipping Information", "productSpecifications": "Product Specifications", "customerReviews": "Customer Reviews", "shippingOptions": "Shipping Options", "standardShipping": "Standard Shipping", "businessDays": "business days", "free": "Free", "expressShipping": "Express Shipping", "overnightShipping": "Overnight Shipping", "nextBusinessDay": "Next business day", "returnPolicy": "Return Policy", "returnPolicyDescription": "We offer a 30-day return policy for all items in original condition.", "product": {"name": "Product Name", "description": "Description", "price": "Price", "comparePrice": "Compare Price", "costPrice": "Cost Price", "category": "Category", "brand": "Brand", "sku": "SKU", "barcode": "Barcode", "stockQuantity": "Stock Quantity", "lowStockThreshold": "Low Stock Threshold", "trackQuantity": "Track Quantity", "inventoryStatus": "Inventory Status", "weight": "Weight", "dimensions": "Dimensions", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "requiresShipping": "Requires Shipping", "images": "Images", "videos": "Videos", "seoTitle": "SEO Title", "seoDescription": "SEO Description", "tags": "Tags", "status": "Status", "featured": "Featured", "digital": "Digital Product"}, "order": {"orderNumber": "Order Number", "status": "Order Status", "paymentStatus": "Payment Status", "customer": "Customer", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "subtotal": "Subtotal", "shippingFee": "Shipping Fee", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "notes": "Notes", "adminNotes": "Admin Notes"}, "seller": {"personalInfo": "Personal Information", "storeInfo": "Store Information", "businessAddress": "Business Address", "bankInfo": "Bank Information", "documents": "Documents", "agreements": "Terms & Agreements", "storeName": "Store Name", "storeDescription": "Store Description", "businessType": "Business Type", "businessLicense": "Business License", "mainCategory": "Main Category", "accountHolderName": "Account Holder Name", "accountNumber": "Account Number", "bankName": "Bank Name", "branchName": "Branch Name"}, "review": {"rating": "Rating", "title": "Review Title", "content": "Review Content", "images": "Review Images", "anonymous": "Anonymous Review"}, "checkout": {"contactEmail": "Contact Email", "subscribeNewsletter": "Subscribe to Newsletter", "firstName": "First Name", "lastName": "Last Name", "company": "Company", "address1": "Address Line 1", "address2": "Address Line 2", "city": "City", "province": "Province", "postalCode": "Postal Code", "phone": "Phone Number", "billingSameAsShipping": "Billing same as shipping", "orderNotes": "Order Notes"}}, "pagination": {"page": "Page", "of": "of", "total": "Total", "showing": "Showing", "to": "to", "entries": "entries", "first": "First", "last": "Last", "previous": "Previous", "next": "Next", "itemsPerPage": "Items per page", "goToPage": "Go to page"}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "toggle": "Toggle theme"}}