#!/bin/bash

echo "Starting comprehensive import fixes..."

# Fix all translation hook imports to use simple-translation
echo "Fixing translation hooks..."
find src -name "*.tsx" -type f -exec sed -i '' 's/useHTranslation/useZeneraTranslation/g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@/lib/i18n/hooks/useHTranslation|@/lib/hooks/simple-translation|g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@/lib/i18n/hooks/useZeneraTranslation|@/lib/hooks/simple-translation|g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@/lib/hooks/use-translation|@/lib/hooks/simple-translation|g' {} \;

# Fix ProductCard imports
echo "Fixing ProductCard imports..."
find src -name "*.tsx" -type f -exec sed -i '' 's|@/components/ui-components/src/components/compositions/ProductCard|@/components/ui/product-card|g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@zenera/ui-components/src/components/compositions/ProductCard|@/components/ui/product-card|g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@zenera/ui-components/compositions|@/components/ui/product-card|g' {} \;

# Fix any remaining useHTranslation references
echo "Final cleanup..."
find src -name "*.tsx" -type f -exec sed -i '' 's|useHTranslation|useZeneraTranslation|g' {} \;

echo "All imports fixed!"
echo "Files processed:"
find src -name "*.tsx" -type f | wc -l
