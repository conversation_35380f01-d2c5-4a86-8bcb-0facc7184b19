#!/bin/bash

# Fix useHTranslation imports
find src -name "*.tsx" -type f -exec sed -i '' 's/useHTranslation/useZeneraTranslation/g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@/lib/i18n/hooks/useHTranslation|@/lib/hooks/use-translation|g' {} \;

# Fix ProductCard imports
find src -name "*.tsx" -type f -exec sed -i '' 's|@/components/ui-components/src/components/compositions/ProductCard|@zenera/ui-components/compositions|g' {} \;
find src -name "*.tsx" -type f -exec sed -i '' 's|@zenera/ui-components/src/components/compositions/ProductCard|@zenera/ui-components/compositions|g' {} \;

echo "Fixed all imports!"
